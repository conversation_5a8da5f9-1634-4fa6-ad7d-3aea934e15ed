import * as client from "openid-client";
import { singpassConfig } from "../config";

function randomUUID() {
  if (window.crypto && window.crypto.randomUUID) {
    return window.crypto.randomUUID();
  }
  // Fallback for older browsers
  return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, (c) =>
    (
      c ^
      (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (c / 4)))
    ).toString(16)
  );
}

function randomBytesHex(length) {
  const array = new Uint8Array(length);
  window.crypto.getRandomValues(array);
  return Array.from(array, (b) => b.toString(16).padStart(2, "0")).join("");
}

// Create configuration using the new v6 API
let config = await client.discovery(
  new URL(singpassConfig.ISSUER_URL),
  singpassConfig.CLIENT_ID,
  undefined // client secret - not used for private_key_jwt
);

async function refreshIssuerConfig() {
  try {
    config = await client.discovery(
      new URL(singpassConfig.ISSUER_URL),
      singpassConfig.CLIENT_ID,
      undefined // client secret - not used for private_key_jwt
    );
  } catch (error) {
    console.error("Failed to refresh issuer configuration:", error);
  }
}

// Recommended to refresh the issuer as opposed to caching indefinitely
const ONE_HOUR_IN_MS = 60 * 60 * 1000;
setInterval(refreshIssuerConfig, ONE_HOUR_IN_MS);

// HTTP timeout configuration is handled differently in v6

export function getJwks(ctx) {
  ctx.body = {
    keys: [
      singpassConfig.KEYS.PUBLIC_SIG_KEY,
      singpassConfig.KEYS.PUBLIC_ENC_KEY,
    ],
  };
}

export async function handleSingpassLogin(ctx) {
  const code_verifier = client.randomPKCECodeVerifier();
  const code_challenge = await client.calculatePKCECodeChallenge(code_verifier);
  const nonce = randomUUID();
  const state = randomBytesHex(16);
  ctx.session.auth = { code_verifier, nonce, state };

  // Authorization request parameters
  const parameters = {
    redirect_uri: singpassConfig.REDIRECT_URI,
    code_challenge_method: "S256",
    code_challenge,
    nonce,
    state,
    scope: singpassConfig.SCOPES,
  };

  // Build authorization URL
  const authorizationUrl = client.buildAuthorizationUrl(config, parameters);
  ctx.redirect(authorizationUrl.href);
}

export async function handleSingpassCallback(ctx) {
  try {
    const currentUrl = new URL(
      ctx.request.url,
      `${ctx.request.protocol}://${ctx.request.host}`
    );
    const { code_verifier, nonce, state } = ctx.session.auth;

    // Create private key JWT for client authentication
    const clientAuth = client.PrivateKeyJwt(
      singpassConfig.KEYS.PRIVATE_SIG_KEY,
      singpassConfig.KEYS.PRIVATE_SIG_KEY.alg || "ES256"
    );

    // Token request using authorization code grant
    const tokens = await client.authorizationCodeGrant(config, currentUrl, {
      pkceCodeVerifier: code_verifier,
      expectedNonce: nonce,
      expectedState: state,
      idTokenExpected: true,
      clientAuth,
    });

    console.log("Token Endpoint Response:", tokens);

    // Get claims from ID token
    const claims = tokens.claims();
    console.log("These are the claims in the ID token:");
    console.log(claims);

    // Userinfo request (available only to apps with additional allowed scopes, beyond just 'openid').
    const userInfo = await client.fetchUserInfo(
      config,
      tokens.access_token,
      claims.sub
    );
    console.log("This is the user info returned:");
    console.log(userInfo);

    ctx.session.user = { ...claims, ...userInfo };
    ctx.redirect("/");
  } catch (err) {
    console.error(err);
    ctx.status = 401;
  }
}
