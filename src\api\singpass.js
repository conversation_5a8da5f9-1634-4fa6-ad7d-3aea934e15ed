import { Issuer, generators, custom } from "openid-client";
import * as crypto from "crypto";
import { singpassConfig } from "../config";

const singpassIssuer = await Issuer.discover(singpassConfig.ISSUER_URL);

let singpassClient = new singpassIssuer.Client(
  {
    // All the hardcoded values used below are taken from Singpass' OpenID Provider Metadata,
    // which can be found at singpassConfig.ISSUER_URL + '/.well-known/openid-configuration'
    client_id: singpassConfig.CLIENT_ID,
    response_types: ["code"],
    token_endpoint_auth_method: "private_key_jwt",
    id_token_signed_response_alg: "ES256",
    userinfo_encrypted_response_alg: singpassConfig.KEYS.PRIVATE_ENC_KEY.alg,
    userinfo_encrypted_response_enc: "A256GCM",
    userinfo_signed_response_alg: singpassConfig.KEYS.PRIVATE_SIG_KEY.alg,
  },
  {
    keys: [
      singpassConfig.KEYS.PRIVATE_SIG_KEY,
      singpassConfig.KEYS.PRIVATE_ENC_KEY,
    ],
  }
);

async function refreshIssuerConfig() {
  try {
    const newIssuerConfig = await Issuer.discover(singpassConfig.ISSUER_URL);
    singpassClient = new newIssuerConfig.Client(
      {
        client_id: singpassConfig.CLIENT_ID,
        response_types: ["code"],
        token_endpoint_auth_method: "private_key_jwt",
        id_token_signed_response_alg: "ES256",
        userinfo_encrypted_response_alg:
          singpassConfig.KEYS.PRIVATE_ENC_KEY.alg,
        userinfo_encrypted_response_enc: "A256GCM",
        userinfo_signed_response_alg: singpassConfig.KEYS.PRIVATE_SIG_KEY.alg,
      },
      {
        keys: [
          singpassConfig.KEYS.PRIVATE_SIG_KEY,
          singpassConfig.KEYS.PRIVATE_ENC_KEY,
        ],
      }
    );
  } catch (error) {
    console.error("Failed to refresh issuer configuration:", error);
  }
}

// Recommended to refresh the issuer as opposed to caching indefinitely
const ONE_HOUR_IN_MS = 60 * 60 * 1000;
setInterval(refreshIssuerConfig, ONE_HOUR_IN_MS);

custom.setHttpOptionsDefaults({
  timeout: 15000,
});

export function getJwks(ctx) {
  ctx.body = {
    keys: [
      singpassConfig.KEYS.PUBLIC_SIG_KEY,
      singpassConfig.KEYS.PUBLIC_ENC_KEY,
    ],
  };
}

export async function handleSingpassLogin(ctx) {
  const code_verifier = generators.codeVerifier();
  const code_challenge = generators.codeChallenge(code_verifier);
  const nonce = crypto.randomUUID();
  const state = crypto.randomBytes(16).toString("hex");
  ctx.session.auth = { code_verifier, nonce, state };

  // Authorization request
  const authorizationUrl = singpassClient.authorizationUrl({
    redirect_uri: singpassConfig.REDIRECT_URI,
    code_challenge_method: "S256",
    code_challenge,
    nonce,
    state,
    scope: singpassConfig.SCOPES,
  });
  ctx.redirect(authorizationUrl);
}

export async function handleSingpassCallback(ctx) {
  try {
    const receivedQueryParams = ctx.request.query;
    const { code_verifier, nonce, state } = ctx.session.auth;

    // Token request
    const tokenSet = await singpassClient.callback(
      singpassConfig.REDIRECT_URI,
      receivedQueryParams,
      {
        code_verifier,
        nonce,
        state,
      }
    );
    console.log("These are the claims in the ID token:");
    console.log(tokenSet.claims());

    // Userinfo request (available only to apps with additional allowed scopes, beyond just 'openid').
    const userInfo = await singpassClient.userinfo(tokenSet);
    console.log("This is the user info returned:");
    console.log(userInfo);

    ctx.session.user = { ...tokenSet.claims(), ...userInfo };
    ctx.redirect("/");
  } catch (err) {
    console.error(err);
    ctx.status = 401;
  }
}
