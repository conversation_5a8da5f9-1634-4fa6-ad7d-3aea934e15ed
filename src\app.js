//Library imports
import React from 'react'
import { createBrowserRouter, Outlet, Router<PERSON>rovider } from 'react-router-dom'
import { ToastContainer, Zoom } from 'react-toastify'

//Internal imports
import * as config from './config'
import Legal from "./page/legal";
import Main from "./page/main";
import Register from './page/register.jsx'
import RegisterDetail from './component/register_detail.jsx'
import RegisterPayment from './component/register_payment.jsx'
import RegisterPalm from './component/register_palm.jsx'
import RegisterSummary from './component/register_summary.jsx'
import Login from './component/login.jsx'

//Design imports
//For toast notifications
import 'react-toastify/dist/ReactToastify.css'

export default function App() {
    /* Variables */

    //Routes
    //Set up all the app routes
    const m_router = createBrowserRouter
    (   [   {   //home route
                path: '/'
            ,   element: <Main />
            ,   children: 
                [   { index: true, element: <Login /> }
                ]
            }
        ,   {   //register route — main container is Register.jsx
                path: config.frontend.route.register.root
            ,    element: <Register />
            ,    children: 
                [   { path: config.frontend.route.register.detail, element: <RegisterDetail /> }
                ,   { path: config.frontend.route.register.payment, element: <RegisterPayment /> }
                ,   { path: config.frontend.route.register.palm, element: <RegisterPalm /> }
                ,   { path: config.frontend.route.register.summary, element: <RegisterSummary /> }
                ]
            }
        ]
    ,   { basename: config.frontend.route.root 
        }
    )

    /* Functions */
    return (
        <>
            {/* Toast notification system */}
            <ToastContainer
                autoClose={5000}
                closeOnClick={false}
                draggable={false}
                pauseOnFocusLoss={false}
                pauseOnHover={false}
                position="top-center"
                transition={Zoom}
            />

            {/* Render the full routing system */}
            <RouterProvider router={m_router} />
        </>
    );
}