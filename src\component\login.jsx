//Library imports
import { useState } from "react";
import { <PERSON><PERSON>, Col, Container, Image, Row, Stack } from "react-bootstrap";
import { PiTagBold } from "react-icons/pi";
import { useNavigate } from "react-router-dom";

//Internal imports
import { useCustomerStore } from "../customer_store";
import { FrontendRoute } from "../data/route";
import RegisterLoading from "./register_loading";
import {singpassConfig} from "../config"

//Design imports
import "../style.css";

//Asset imports
import logo from "../asset/image/logo.png";
import singpass from "../asset/image/singpass.svg";
import { handleSingpassLogin } from "../api/singpass";

export default function Login() {
  /* Variables */

  //Hooks
  const m_navigate = useNavigate();

  //States
  const [m_loading_register, set_loading_register] = useState(false);

  //State hook to clear session and reset store
  const resetStore = useCustomerStore((state) => state.resetStore);

  /* Functions */

  function HandleRegisterButton() {
    //Set loading screen
    set_loading_register(true);

    //Reset all previous data
    resetStore();

    //Navigate to first step of registration
    m_navigate(FrontendRoute.register.detail);
  }

  function HandleSingpassButton() {
    //Set loader
    set_loading_register((previous) => true);

    handleSingpassLogin.then((authUrl) => {
      m_navigate(authUrl);
      set_loading_register((previous) => false);
    });

    // const code_verifier = generators.codeVerifier();
    // const code_challenge = generators.codeChallenge(code_verifier);
    // const nonce = crypto.randomUUID();
    // const state = crypto.randomBytes(16).toString("hex");

    // // Authorization request
    // const authorizationUrl = singpassClient.authorizationUrl({
    //   redirect_uri: singpassConfig.singpassRedirect,
    //   code_challenge_method: "S256",
    //   code_challenge,
    //   nonce,
    //   state,
    //   scope: singpassConfig.scopes,
    // });

    // m_navigate(authorizationUrl);

    // //Set session ID in data store
    // g_setSessionId(response.data.data.session_id);

    // //Navigate to next page
    // m_navigate(FrontendRoute.register.facial);
    // set_loading_register((previous) => false);
  }

  if (m_loading_register) return <RegisterLoading />;

  /* Main UI */
  return (
    <>
      <Container className="register-login-background">
        <div className="register-login-main">
          <Image
            className="register-login-header-logo"
            src={logo}
            alt="PalmPay logo"
          />
          <label className="register-login-title">PalmPay Registration</label>
          <Button
            className="register-login-button"
            onClick={HandleRegisterButton}
          >
            Register
          </Button>
        </div>

        <div className="register-login-footer-container">
          <PiTagBold size={22} style={{ transform: "rotate(90deg)" }} />
          <label className="register-login-footer-text">
            {" "}
            Palm authentication payment system
          </label>
        </div>
      </Container>
    </>
  );
}

/* 
    return (
        <Container className="m-0 p-0 register-login-background">
            <Container className="register-login-container-header p-4">
                <Container>
                    <Row>
                        <Col className="align-items-center d-flex justify-content-center">
                            <label className="register-login-header-text">Palm Scan Registration</label>
                        </Col>
                        <Col className="align-items-center d-flex justify-content-center">
                            <Image className="register-login-header-logo" src={logo} />
                        </Col>
                    </Row>
                </Container>

                <Stack className="align-items-center register-login-container-body" direction="vertical" gap={4}>
                    <Button className="register-login-button-createAccount" onClick={HandleRegisterButton}>
                        Register
                    </Button>
                    <Stack className="register-login-footer-container align-items-center justify-content-center m-0 p-0" direction="horizontal" gap={2}>
                        <PiTagBold size={22} style={{ transform: "rotate(90deg)" }} />
                        <label className="register-login-footer-text">Palm authentication payment system</label>
                    </Stack>
                </Stack>
            </Container>
        </Container>
    );
}
    */
