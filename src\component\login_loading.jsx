////////////////////////////////////////////////////////////////////////////////////////////////////
/*!	\file		login_loading.jsx
*	\brief		Login loading page
*	\date		2024
*	\author		daryltan
*	\copyright	Developed for JTC SDD FRP
*	
****************************************************************************************************
*
*	\par	Changelog
*	
*	18 Jun'24 : darytltan - added function
*/
////////////////////////////////////////////////////////////////////////////////////////////////////

//Library imports
import { useEffect , useState } from 'react'
import { Col , Container , Row } from 'react-bootstrap'
import { useLocation } from 'react-router-dom'
import { PulseLoader } from 'react-spinners'

//Internal imports
import { FrontendRoute } from '../data/route'

//Design imports
import "../style.css"

export default function LoginLoading()
{
	/* Variables */

	//Hooks
	const m_location = useLocation()

	//States
	const [m_currentPath , set_currentPath] = useState( '' )

	/* Functions */
	
	useEffect
	(	() => 
		{
			//Set current path for tracking
			set_currentPath( m_location.pathname )
		}		
	,	[m_location]
	)

	function RenderLoadingPage()
	{
		//Return element
		return (
			<>
				<Container className='register-loading-container'>
					<Row className='pb-5'>
						<Col className='align-items-center d-flex justify-content-center'>
							<PulseLoader color='#0055B8' loading={true} size={25} speedMultiplier={0.8}/>
						</Col>
					</Row>
					<Row className='align-items-center d-flex justify-content-center'>
						<RenderLoadingText />
					</Row>
				</Container>
			</>
		)
	}

	function RenderLoadingText()
	{
		switch ( m_currentPath )
		{
			//Case for authenticate route
			case FrontendRoute.login.authenticate:
			{
				//Return element
				return <label className='register-loading-container-text'>Verifying your identity...</label>
			}//end case

			//Default case
			default: return ( <></> )
		}//end switch
	}
	
	//Return element
	return (
		<>
			<RenderLoadingPage />
		</>
	)
}