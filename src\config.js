//Backend config
export const backend = {
  route: {
    root: "/customer",
    register: {
      root: "/register",
      detail: "/detail",
      payment: "/payment",
      otp: "/otp",
      palm: "/palm",
      complete: "/complete",
      result: "/result",
    },
  },
};

//Frontend config
export const frontend = {
  route: {
    root: process.env.REACT_APP_ROUTE_BASE_PATH,
    legal: { root: "legal" },
    register: {
      root: "register",
      detail: "detail",
      payment: "payment",
      palm: "palm",
      summary: "summary",
    },
  },
};

export const singpassConfig = {
  ISSUER_URL: process.env.REACT_APP_SINGPASS_ISSUER_URL,
  SCOPES: process.env.REACT_APP_SINGPASS_SCOPES,
  REDIRECT_URI: process.env.REACT_APP_SINGPASS_REDIRECT_URI,
  CLIENT_ID: process.env.REACT_APP_SINGPASS_CLIENT_ID,
  KEYS: {
    PRIVATE_ENC_KEY: JSON.parse(
      process.env.REACT_APP_SINGPASS_KEYS_PRIVATE_ENC_KEY
    ),
    PRIVATE_SIG_KEY: JSON.parse(
      process.env.REACT_APP_SINGPASS_KEYS_PRIVATE_SIG_KEY
    ),
    PUBLIC_SIG_KEY: JSON.parse(
      process.env.REACT_APP_SINGPASS_KEYS_PUBLIC_SIG_KEY
    ),
    PUBLIC_ENC_KEY: JSON.parse(
      process.env.REACT_APP_SINGPASS_KEYS_PUBLIC_ENC_KEY
    ),
  },
};
